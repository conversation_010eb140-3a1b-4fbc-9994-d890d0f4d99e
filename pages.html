<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>News Channel</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css?family=Amarante&display=swap" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
	<link rel="stylesheet" type="text/css" href="css/font-awesome.min.css" />
    <script src="js/jquery-2.1.1.min.js"></script>
	<script src="js/bootstrap.min.js"></script>

  </head>
<body>
<section id="header" class="cd-secondary-nav">
  <div class="container-fluid">
  <nav class="navbar navbar">
    <div class="navbar-header">
    	<button class="navbar-toggle" type="button" data-toggle="collapse" data-target=".js-navbar-collapse">
			<span class="sr-only">Toggle navigation</span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
		</button>
		<a class="navbar-brand" href="index.html"><span>N</span>ews</a>
	</div>
	
	<div class="collapse navbar-collapse js-navbar-collapse">
		<ul class="nav navbar-nav navbar-right">
		    <li><a href="index.html">HOME</a></li>
			<li><a href="list.html">LIST</a></li>
			<li><a href="detail.html">DETAIL</a></li>
			<li><a href="contact.html">CONTACT</a></li>
			<li><a class="active_1" href="pages.html">TYPOGRAPHY</a></li>
			<li class="dropdown"><a href="#" data-toggle="dropdown"><span class="fa fa-search"></span></a>
							<ul class="dropdown-menu drop_1" style="min-width: 300px;">
								<li>
									<div class="row_1">
										<div class="col-sm-12">
											<form class="navbar-form navbar-left" role="search">
											<div class="input-group">
												<input type="text" class="form-control" placeholder="Search">
												<span class="input-group-btn">
													<button class="btn btn-primary" type="button">
														Search</button>
												</span>
											</div>
											</form>
										</div>
									</div>
								</li>
							</ul>
		  </li>
			<li class="dropdown dropdown-large temp">
				<a href="#" class="dropdown-toggle atag" data-toggle="dropdown">MORE<b class="caret"></b></a>
				
				<ul class="dropdown-menu dropdown-menu-large row col-sm-12 font_1 drop_2">
					<li class="col-sm-3">
						<ul>
							<li class="dropdown-header">Glyphicons</li>
							<li><a href="#">Available glyphs</a></li>
							<li class="disabled"><a href="#">How to use</a></li>
							<li><a href="#">Examples</a></li>
							<li class="divider"></li>
							<li class="dropdown-header">Dropdowns</li>
							<li><a href="#">Example</a></li>
							<li><a href="#">Aligninment options</a></li>
							<li><a href="#">Headers</a></li>
							<li><a href="#">Disabled menu items</a></li>
						</ul>
					</li>
					<li class="col-sm-3">
						<ul>
							<li class="dropdown-header">Button groups</li>
							<li><a href="#">Basic example</a></li>
							<li><a href="#">Button toolbar</a></li>
							<li><a href="#">Sizing</a></li>
							<li><a href="#">Nesting</a></li>
							<li><a href="#">Vertical variation</a></li>
							<li class="divider"></li>
							<li class="dropdown-header">Button dropdowns</li>
							<li><a href="#">Single button dropdowns</a></li>
						</ul>
					</li>
					<li class="col-sm-3">
						<ul>
							<li class="dropdown-header">Input groups</li>
							<li><a href="#">Basic example</a></li>
							<li><a href="#">Sizing</a></li>
							<li><a href="#">Checkboxes and radio addons</a></li>
							<li class="divider"></li>
							<li class="dropdown-header">Navs</li>
							<li><a href="#">Tabs</a></li>
							<li><a href="#">Pills</a></li>
							<li><a href="#">Justified</a></li>
						</ul>
					</li>
					<li class="col-sm-3">
						<ul>
							<li class="dropdown-header">Navbar</li>
							<li><a href="#">Default navbar</a></li>
							<li><a href="#">Buttons</a></li>
							<li><a href="#">Text</a></li>
							<li><a href="#">Non-nav links</a></li>
							<li><a href="#">Component alignment</a></li>
							<li><a href="#">Fixed to top</a></li>
							<li><a href="#">Fixed to bottom</a></li>
							<li><a href="#">Static top</a></li>
							<li><a href="#">Inverted navbar</a></li>
						</ul>
					</li>
				</ul>
			</li>
			<li class="dropdown">
					  <a class="tag_menu" href="#" data-toggle="dropdown" role="button" aria-expanded="false">DROPDOWN<span class="caret"></span></a>
					  <ul class="dropdown-menu drop_1" role="menu">
						<li><a href="index.html">Home</a></li>
						<li><a href="list.html">List</a></li>
						<li><a href="detail.html">Detail</a></li>
					  </ul>
          </li>
		</ul>
	</div><!-- /.nav-collapse -->
  </nav>
</div>
</section>

<section id="pages">
     <div class="container">
	   <div class="row">
	     <div class="col-sm-12">
		   <div class="pages">
		     <div class="container" role="main">
			   <h1 class="text-center typo">TYPOGRAPHY</h1>
  
		  <div class="page-header">
			<h1>How to use with Bootstrap</h1>
		  </div>
		  <div class="well">
			
			<p>
			  Add this to your head section, make sure it is last .css file linked.
			</p>
			
			<ol>
			  <li>create a .css file withthe lavish css in it - call it lavish-theme.css</li>
			  <li>in your  link to that file in your .htm file as shown below</li>
			</ol>
			
			<code>
			  <!-- Custom styles for this template --><br>
			</code>
			
		  </div>
		  
		  <div class="page-header">
			<h1>Buttons</h1>
		  </div>
		  <p>
			<button type="button" class="btn btn-lg btn-default">Default</button>
			<button type="button" class="btn btn-lg btn-primary">Primary</button>
			<button type="button" class="btn btn-lg btn-success">Success</button>
			<button type="button" class="btn btn-lg btn-info">Info</button>
			<button type="button" class="btn btn-lg btn-warning">Warning</button>
			<button type="button" class="btn btn-lg btn-danger">Danger</button>
			<button type="button" class="btn btn-lg btn-link">Link</button>
		  </p>
		  <p>
			<button type="button" class="btn btn-default">Default</button>
			<button type="button" class="btn btn-primary">Primary</button>
			<button type="button" class="btn btn-success">Success</button>
			<button type="button" class="btn btn-info">Info</button>
			<button type="button" class="btn btn-warning">Warning</button>
			<button type="button" class="btn btn-danger">Danger</button>
			<button type="button" class="btn btn-link">Link</button>
		  </p>
		  <p>
			<button type="button" class="btn btn-sm btn-default">Default</button>
			<button type="button" class="btn btn-sm btn-primary">Primary</button>
			<button type="button" class="btn btn-sm btn-success">Success</button>
			<button type="button" class="btn btn-sm btn-info">Info</button>
			<button type="button" class="btn btn-sm btn-warning">Warning</button>
			<button type="button" class="btn btn-sm btn-danger">Danger</button>
			<button type="button" class="btn btn-sm btn-link">Link</button>
		  </p>
		  <p>
			<button type="button" class="btn btn-xs btn-default">Default</button>
			<button type="button" class="btn btn-xs btn-primary">Primary</button>
			<button type="button" class="btn btn-xs btn-success">Success</button>
			<button type="button" class="btn btn-xs btn-info">Info</button>
			<button type="button" class="btn btn-xs btn-warning">Warning</button>
			<button type="button" class="btn btn-xs btn-danger">Danger</button>
			<button type="button" class="btn btn-xs btn-link">Link</button>
		  </p>
		  
		  
		  <div class="page-header">
			<h1>Tables</h1>
		  </div>
		  <div class="row">
			<div class="col-md-6">
			  <table class="table">
				<thead>
				  <tr>
					<th>#</th>
					<th>First Name</th>
					<th>Last Name</th>
					<th>Username</th>
				  </tr>
				</thead>
				<tbody>
				  <tr>
					<td>1</td>
					<td>Mark</td>
					<td>Otto</td>
					<td>@mdo</td>
				  </tr>
				  <tr>
					<td>2</td>
					<td>Jacob</td>
					<td>Thornton</td>
					<td>@fat</td>
				  </tr>
				  <tr>
					<td>3</td>
					<td>Larry</td>
					<td>the Bird</td>
					<td>@twitter</td>
				  </tr>
				</tbody>
			  </table>
			</div>
			<div class="col-md-6">
			  <table class="table table-striped">
				<thead>
				  <tr>
					<th>#</th>
					<th>First Name</th>
					<th>Last Name</th>
					<th>Username</th>
				  </tr>
				</thead>
				<tbody>
				  <tr>
					<td>1</td>
					<td>Mark</td>
					<td>Otto</td>
					<td>@mdo</td>
				  </tr>
				  <tr>
					<td>2</td>
					<td>Jacob</td>
					<td>Thornton</td>
					<td>@fat</td>
				  </tr>
				  <tr>
					<td>3</td>
					<td>Larry</td>
					<td>the Bird</td>
					<td>@twitter</td>
				  </tr>
				</tbody>
			  </table>
			</div>
		  </div>
		  
		  <div class="row">
			<div class="col-md-6">
			  <table class="table table-bordered">
				<thead>
				  <tr>
					<th>#</th>
					<th>First Name</th>
					<th>Last Name</th>
					<th>Username</th>
				  </tr>
				</thead>
				<tbody>
				  <tr>
					<td rowspan="2">1</td>
					<td>Mark</td>
					<td>Otto</td>
					<td>@mdo</td>
				  </tr>
				  <tr>
					<td>Mark</td>
					<td>Otto</td>
					<td>@TwBootstrap</td>
				  </tr>
				  <tr>
					<td>2</td>
					<td>Jacob</td>
					<td>Thornton</td>
					<td>@fat</td>
				  </tr>
				  <tr>
					<td>3</td>
					<td colspan="2">Larry the Bird</td>
					<td>@twitter</td>
				  </tr>
				</tbody>
			  </table>
			</div>
			<div class="col-md-6">
			  <table class="table table-condensed">
				<thead>
				  <tr>
					<th>#</th>
					<th>First Name</th>
					<th>Last Name</th>
					<th>Username</th>
				  </tr>
				</thead>
				<tbody>
				  <tr>
					<td>1</td>
					<td>Mark</td>
					<td>Otto</td>
					<td>@mdo</td>
				  </tr>
				  <tr>
					<td>2</td>
					<td>Jacob</td>
					<td>Thornton</td>
					<td>@fat</td>
				  </tr>
				  <tr>
					<td>3</td>
					<td colspan="2">Larry the Bird</td>
					<td>@twitter</td>
				  </tr>
				</tbody>
			  </table>
			</div>
		  </div>
		  
		  
		  <div class="page-header">
			<h1>Thumbnails</h1>
		  </div>
		  <img src="img/88.jpg" class="img-thumbnail" alt="A generic square placeholder image with a white border around it, making it resemble a photograph taken with an old instant camera">
		  
		  
		  <div class="page-header">
			<h1>Labels</h1>
		  </div>
		  <h1>
			<span class="label label-default">Default</span>
			<span class="label label-primary">Primary</span>
			<span class="label label-success">Success</span>
			<span class="label label-info">Info</span>
			<span class="label label-warning">Warning</span>
			<span class="label label-danger">Danger</span>
		  </h1>
		  <h2>
			<span class="label label-default">Default</span>
			<span class="label label-primary">Primary</span>
			<span class="label label-success">Success</span>
			<span class="label label-info">Info</span>
			<span class="label label-warning">Warning</span>
			<span class="label label-danger">Danger</span>
		  </h2>
		  <h3>
			<span class="label label-default">Default</span>
			<span class="label label-primary">Primary</span>
			<span class="label label-success">Success</span>
			<span class="label label-info">Info</span>
			<span class="label label-warning">Warning</span>
			<span class="label label-danger">Danger</span>
		  </h3>
		
		  <h4>
			<span class="label label-default">Default</span>
			<span class="label label-primary">Primary</span>
			<span class="label label-success">Success</span>
			<span class="label label-info">Info</span>
			<span class="label label-warning">Warning</span>
			<span class="label label-danger">Danger</span>
		  </h4>
		  <h5>
			<span class="label label-default">Default</span>
			<span class="label label-primary">Primary</span>
			<span class="label label-success">Success</span>
			<span class="label label-info">Info</span>
			<span class="label label-warning">Warning</span>
			<span class="label label-danger">Danger</span>
		  </h5>
		  <h6>
			<span class="label label-default">Default</span>
			<span class="label label-primary">Primary</span>
			<span class="label label-success">Success</span>
			<span class="label label-info">Info</span>
			<span class="label label-warning">Warning</span>
			<span class="label label-danger">Danger</span>
		  </h6>
		  <p>
			<span class="label label-default">Default</span>
			<span class="label label-primary">Primary</span>
			<span class="label label-success">Success</span>
			<span class="label label-info">Info</span>
			<span class="label label-warning">Warning</span>
			<span class="label label-danger">Danger</span>
		  </p>
		  

		  
		  <div class="page-header">
			<h1>Badges</h1>
		  </div>
		  <p>
			<a href="#">Inbox <span class="badge">42</span></a>
		  </p>
		  <ul class="nav nav-pills" role="tablist">
			<li role="presentation" class="active"><a href="#">Home <span class="badge">42</span></a></li>
			<li role="presentation"><a href="#">Profile</a></li>
			<li role="presentation"><a href="#">Messages <span class="badge">3</span></a></li>
		  </ul>
		  
		  
		  <div class="page-header">
			<h1>Dropdown menus</h1>
		  </div>
		  <div class="dropdown theme-dropdown clearfix">
			<a id="dropdownMenu1" href="#" class="sr-only dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">Dropdown <span class="caret"></span></a>
			<ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu1">
			  <li class="active" role="presentation"><a role="menuitem" tabindex="-1" href="#">Action</a></li>
			  <li role="presentation"><a role="menuitem" tabindex="-1" href="#">Another action</a></li>
			  <li role="presentation"><a role="menuitem" tabindex="-1" href="#">Something else here</a></li>
			  <li role="presentation" class="divider"></li>
			  <li role="presentation"><a role="menuitem" tabindex="-1" href="#">Separated link</a></li>
			</ul>
		  </div>
		  
		  
		  <div class="page-header">
			<h1>Navs</h1>
		  </div>
		  <ul class="nav nav-tabs" role="tablist">
			<li role="presentation" class="active"><a href="#">Home</a></li>
			<li role="presentation"><a href="#">Profile</a></li>
			<li role="presentation"><a href="#">Messages</a></li>
		  </ul>
		  <ul class="nav nav-pills" role="tablist">
			<li role="presentation" class="active"><a href="#">Home</a></li>
			<li role="presentation"><a href="#">Profile</a></li>
			<li role="presentation"><a href="#">Messages</a></li>
		  </ul>
		  
		  
		  
		  
		  <div class="page-header">
			<h1>Alerts</h1>
		  </div>
		  <div class="alert alert-success" role="alert">
			<strong>Well done!</strong> You successfully read this important alert message.
		  </div>
		  <div class="alert alert-info" role="alert">
			<strong>Heads up!</strong> This alert needs your attention, but it's not super important.
		  </div>
		  <div class="alert alert-warning" role="alert">
			<strong>Warning!</strong> Best check yo self, you're not looking too good.
		  </div>
		  <div class="alert alert-danger" role="alert">
			<strong>Oh snap!</strong> Change a few things up and try submitting again.
		  </div>
		  
		  
		  <div class="page-header">
			<h1>Progress bars</h1>
		  </div>
		  <div class="progress">
			<div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 60%;"><span class="sr-only">60% Complete</span></div>
		  </div>
		  <div class="progress">
			<div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width: 40%"><span class="sr-only">40% Complete (success)</span></div>
		  </div>
		  <div class="progress">
			<div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" style="width: 20%"><span class="sr-only">20% Complete</span></div>
		  </div>
		  <div class="progress">
			<div class="progress-bar progress-bar-warning" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 60%"><span class="sr-only">60% Complete (warning)</span></div>
		  </div>
		  <div class="progress">
			<div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100" style="width: 80%"><span class="sr-only">80% Complete (danger)</span></div>
		  </div>
		  <div class="progress">
			<div class="progress-bar progress-bar-striped" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 60%"><span class="sr-only">60% Complete</span></div>
		  </div>
		  <div class="progress">
			<div class="progress-bar progress-bar-success" style="width: 35%"><span class="sr-only">35% Complete (success)</span></div>
			<div class="progress-bar progress-bar-warning" style="width: 20%"><span class="sr-only">20% Complete (warning)</span></div>
			<div class="progress-bar progress-bar-danger" style="width: 10%"><span class="sr-only">10% Complete (danger)</span></div>
		  </div>
		  
		  
		  <div class="page-header">
			<h1>List groups</h1>
		  </div>
		  <div class="row">
			<div class="col-sm-4">
			  <ul class="list-group">
				<li class="list-group-item">Cras justo odio</li>
				<li class="list-group-item">Dapibus ac facilisis in</li>
				<li class="list-group-item">Morbi leo risus</li>
				<li class="list-group-item">Porta ac consectetur ac</li>
				<li class="list-group-item">Vestibulum at eros</li>
			  </ul>
			</div><!-- /.col-sm-4 -->
			<div class="col-sm-4">
			  <div class="list-group">
				<a href="#" class="list-group-item active">
				  Cras justo odio
				</a>
				<a href="#" class="list-group-item">Dapibus ac facilisis in</a>
				<a href="#" class="list-group-item">Morbi leo risus</a>
				<a href="#" class="list-group-item">Porta ac consectetur ac</a>
				<a href="#" class="list-group-item">Vestibulum at eros</a>
			  </div>
			</div><!-- /.col-sm-4 -->
			<div class="col-sm-4">
			  <div class="list-group">
				<a href="#" class="list-group-item active">
				  <h4 class="list-group-item-heading">List group item heading</h4>
				  <p class="list-group-item-text">Donec id elit non mi porta gravida at eget metus. Maecenas sed diam eget risus varius blandit.</p>
				</a>
				<a href="#" class="list-group-item">
				  <h4 class="list-group-item-heading">List group item heading</h4>
				  <p class="list-group-item-text">Donec id elit non mi porta gravida at eget metus. Maecenas sed diam eget risus varius blandit.</p>
				</a>
				<a href="#" class="list-group-item">
				  <h4 class="list-group-item-heading">List group item heading</h4>
				  <p class="list-group-item-text">Donec id elit non mi porta gravida at eget metus. Maecenas sed diam eget risus varius blandit.</p>
				</a>
			  </div>
			</div><!-- /.col-sm-4 -->
		  </div>
		  
		  
		  <div class="page-header">
			<h1>Panels</h1>
		  </div>
		  <div class="row">
			<div class="col-sm-4">
			  <div class="panel panel-default">
				<div class="panel-heading">
				  <h3 class="panel-title">Panel title</h3>
				</div>
				<div class="panel-body">
				  Panel content
				</div>
			  </div>
			  <div class="panel panel-primary">
				<div class="panel-heading">
				  <h3 class="panel-title">Panel title</h3>
				</div>
				<div class="panel-body">
				  Panel content
				</div>
			  </div>
			</div><!-- /.col-sm-4 -->
			<div class="col-sm-4">
			  <div class="panel panel-success">
				<div class="panel-heading">
				  <h3 class="panel-title">Panel title</h3>
				</div>
				<div class="panel-body">
				  Panel content
				</div>
			  </div>
			  <div class="panel panel-info">
				<div class="panel-heading">
				  <h3 class="panel-title">Panel title</h3>
				</div>
				<div class="panel-body">
				  Panel content
				</div>
			  </div>
			</div><!-- /.col-sm-4 -->
			<div class="col-sm-4">
			  <div class="panel panel-warning">
				<div class="panel-heading">
				  <h3 class="panel-title">Panel title</h3>
				</div>
				<div class="panel-body">
				  Panel content
				</div>
			  </div>
			  <div class="panel panel-danger">
				<div class="panel-heading">
				  <h3 class="panel-title">Panel title</h3>
				</div>
				<div class="panel-body">
				  Panel content
				</div>
			  </div>
			</div><!-- /.col-sm-4 -->
		  </div>
		  
		  
		  <div class="page-header">
			<h1>Wells</h1>
		  </div>
		  <div class="well">
			<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Maecenas sed diam eget risus varius blandit sit amet non magna. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Cras mattis consectetur purus sit amet fermentum. Duis mollis, est non commodo luctus, nisi erat porttitor ligula, eget lacinia odio sem nec elit. Aenean lacinia bibendum nulla sed consectetur.</p>
			<hr>
			<p><a href="#" target="_blank"><small>HTML</small><sup>5</sup></a></p>  
			
		  </div>
		  
		  
		</div>
					</div>
		 </div>
	   </div>
	  </div>
   </section>
   
<section id="magazine" class="clearfix">
 <div class="container-fluid">
  <div class="row">
   <div class="col-sm-12">
    <div class="col-sm-5 magzine_1">
	  <h1>NEWS READER</h1>
	  <p> Fusce nec tellus sed augue semper porta. Mauris massa.Vestibulum lacinia arcu eget nulla.Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Curabitursodales ligula in libero.</p>
	  <h4><a href="#">Attend your book</a></h4>
	</div>
	<div class="col-sm-7">
	 <div class="mag_n clearfix">
	  <div class="col-sm-4">
	      <h3>Travel</h3>
		  <ul>
		   <li><a href="#">Destinations</a></li>
		   <li><a href="#">Food & Drink</a></li>
		   <li><a href="#">Play</a></li>
		   <li><a href="#">Stay</a></li>
		   <li><a href="#">Videos</a></li>
		  </ul>
	  </div>
	  <div class="col-sm-4">
	      <h3>Business</h3>
		  <ul>
		   <li><a href="#">Markerts</a></li>
		   <li><a href="#">Media</a></li>
		   <li><a href="#">Success</a></li>
		   <li><a href="#">Progress</a></li>
		   <li><a href="#">About Us</a></li>
		  </ul>
	  </div>
	  <div class="col-sm-4">
	      <h3>Features</h3>
		  <ul>
		   <li><a href="#">Trending</a></li>
		   <li><a href="#">Popular</a></li>
		   <li><a href="#">New</a></li>
		   <li><a href="#">Latest</a></li>
		   <li><a href="#">All Features</a></li>
		  </ul>
	  </div>
	 </div>
	 <div class="mag_n clearfix">
	  <div class="col-sm-4">
	      <h3>Sports</h3>
		  <ul>
		   <li><a href="#">Football</a></li>
		   <li><a href="#">Tennis</a></li>
		   <li><a href="#">Cricket</a></li>
		   <li><a href="#">Golf</a></li>
		   <li><a href="#">Rugby</a></li>
		  </ul>
	  </div>
	  <div class="col-sm-4">
	      <h3>World</h3>
		  <ul>
		   <li><a href="#">India</a></li>
		   <li><a href="#">America</a></li>
		   <li><a href="#">Australia</a></li>
		   <li><a href="#">England</a></li>
		   <li><a href="#">Africa</a></li>
		  </ul>
	  </div>
	  <div class="col-sm-4">
	      <h3>More</h3>
		  <ul>
		   <li><a href="#">Videos</a></li>
		   <li><a href="#">Photos</a></li>
		   <li><a href="#">About Us</a></li>
		   <li><a href="#">Gallery</a></li>
		   <li><a href="#">Education</a></li>
		  </ul>
	  </div>
	 </div>
	</div>
   </div>
   <div class="col-sm-12 magzine_2">
    <div class="magzine_2_inner text-center">
	<p>© 2013 Your Website Name. All Rights Reserved | Design by <a href="http://www.templateonweb.com">TemplateOnWeb</a></p>
	</div>
	<div class="magzine_3 text-center">
    <div class="magzine_3_inner">
	<a href="#">About US </a> <span>/</span>
	<a href="#">Our Policy  </a> <span>/</span>
	<a href="#">advertise </a> <span>/</span>
	<a href="#">Terms and condition </a> <span>/</span>
	<a href="#">On paper Agreement </a> <span>/</span>
	<a href="#">Contact us</a>
	</div>
   </div>
   </div>
  </div>
 </div>
</section>

<script>
$(document).ready(function(){

/*****Fixed Menu******/
var secondaryNav = $('.cd-secondary-nav'),
   secondaryNavTopPosition = secondaryNav.offset().top;
	$(window).on('scroll', function(){
		if($(window).scrollTop() > secondaryNavTopPosition ) {
			secondaryNav.addClass('is-fixed');	
		} else {
			secondaryNav.removeClass('is-fixed');
		}
	});	
	
});
</script>
</body>
       
</html>
