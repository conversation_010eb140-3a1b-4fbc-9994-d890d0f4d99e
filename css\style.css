/*
Template Name:News Channel
File: Layout CSS
Author: Templates On Web
Author URI: http://templateonweb.com/
Licence: <a href="http://www.templateonweb.com/terms.php?v=content&contentid=152">Website Template Licence</a>
*/
body{
font-family: 'Amarante', cursive;
   }
.navbar-toggle{
  background:#e1e1e1;
		}
 .navbar-toggle .icon-bar{
	background:#000000;
		}
a:hover{
     text-decoration:none;
	 }
ul{
  padding:0;
  margin:0;
  list-style:none;
  }
.border_left_1{
   border-left:1px solid #e1e1e1;
   }
.border_none_1{
   border:none!important;
   }
 .navbar-collapse{
   max-height:none;
   }
.active_1{
 color:#fff!important; 
 font-weight:bold!important;
 background:#dc131b;
    }
 .container-fluid{
   padding-left:40px;
   padding-right:40px;
	}
.border_bottom_1{
   border-bottom:1px solid #e1e1e1;
   }
 .pad_1{
  padding-top:0!important; 
   }
/********************* header****************/
.cd-secondary-nav .is-visible {
  visibility: visible;
  transform: scale(1);
  transition: transform 0.3s, visibility 0s 0s;
}
 .cd-secondary-nav.is-fixed {
    z-index: 9999;
    position: fixed;
    left: auto;
    top: 0; 
	background:#000000e6;
	width:100%;
  }	
#header{
   background: #f4f4f4;
   box-shadow: 0 3px 13px 0 #dc131b26;
   }
.navbar{
  min-height:auto;
  margin-bottom:0; 
   }
#header .navbar-nav {
padding-top:12px;
   }
 .header_new ul li a{
 color:#000000!important;
   }
#header .navbar-brand{
  font-size:30px;
  font-weight:900!important;
  border-radius:100%;
  color:#000;
  background:#ffd800;
  padding:25px 8px 50px 8px;
  margin-right:20px;
   }
#header .navbar-brand span{
border-right:2px solid;
margin-right:2px;
padding-right:2px;
   }
#header ul li a{
color:#dc131b;
font-size:18px;
margin-right:5px;
margin-left:5px;
letter-spacing:1px;
border-radius:3px;
   }
#header ul li a i{
font-size:20px;
   }
#header ul li a:hover{
background:#dc131b;
color:#fff;
   }
 .nav .open>a, .nav .open>a:hover, .nav .open>a:focus {
  background:#dc131b;
  color:#ffff!important;
}
#header .mega-dropdown {
  position: static !important;
}
#header .mega-dropdown-menu {
    padding: 20px 0px;
    width: 100%;
    box-shadow: none;
    -webkit-box-shadow: none;
}
#header .mega-dropdown-menu > li > ul {
  padding: 0;
  margin: 0;
}
#header .mega-dropdown-menu > li > ul > li {
  list-style: none;
}
#header .mega-dropdown-menu > li > ul > li > a {
  display: block;
  color: #222;
  padding: 3px 5px;
}
#header .mega-dropdown-menu > li ul > li > a:hover,
#header .mega-dropdown-menu > li ul > li > a:focus {
  text-decoration: none;
}
#header .mega-dropdown-menu .dropdown-header {
  font-size: 18px;
  color: #ff3546;
  padding: 5px 60px 5px 5px;
  line-height: 30px;
}

 #header   .dropdown-large {
  position: static !important;
}
#header .dropdown-menu-large {
  margin-left: 16px;
  margin-right: 16px;
  padding: 20px 0px;
  margin-top:2px;
}
.drop_1{
 margin-top:15px!important; 
  }
#header .dropdown-menu-large > li > ul {
  padding: 0;
  margin: 0;
}
#header .dropdown-menu-large > li > ul > li {
  list-style: none;
}
#header .dropdown-menu-large > li > ul > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.428571429;
  color: #333333;
  white-space: normal;
  font-size:16px;
}
#header .dropdown-menu-large > li ul > li > a:hover,
#header .dropdown-menu-large > li ul > li > a:focus {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5;
}
#header .dropdown-menu-large .disabled > a,
#header .dropdown-menu-large .disabled > a:hover,
#header .dropdown-menu-large .disabled > a:focus {
  color: #999999;
}
#header .dropdown-menu-large .disabled > a:hover,
#header .dropdown-menu-large .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed;
}
#header .dropdown-menu-large .dropdown-header {
  color: #dc131b;
  font-size: 24px;
}
@media (max-width: 768px) {
#header   .dropdown-menu-large {
    margin-left: 0 ;
    margin-right: 0 ;
  }
 #header  .dropdown-menu-large > li {
    margin-bottom: 30px;
  }
 #header  .dropdown-menu-large > li:last-child {
    margin-bottom: 0;
  }
#header   .dropdown-menu-large .dropdown-header {
    padding: 3px 15px !important;
  }
}
.header_new ul li a{
   color:#333333!important;
   }
#header .btn-primary{
  background:#dc131b;
  border-color:#dc131b;
  color:#fff;
  }
/********************* header_end****************/
/*********************center_home****************/
.center_home{
 padding-top:30px;
 }
.center_home_1i h1{
 padding:0;
 margin:0;
 margin-bottom:20px; 
 line-height:1em; 
   }
.center_home_1i h1 a{
 color:#333;
   }
.center_home_1i h1 a:hover{
 color:#dc131b;
   }
.center_home_1i p{
padding:0;
margin:0;
font-size:17px;
margin-top:10px;
   }
.center_home_1i ul{
 margin-top:10px; 
  }
.center_home_1i ul li{
border-bottom:1px solid #eee;
padding-bottom:5px; 
padding-top:5px;
font-size:16px; 
  }
.center_home_1i ul li a{
 color:#666; 
  }
.center_home_1i ul li a:hover{
 color:#dc131b; 
  }
/********************* center_home_end****************/
/********************* stories****************/
#stories{
   padding-top:30px;
   padding-bottom:20px;
   }
#stories .stories_1 h2{
      padding:0;
	  margin:0; 
	  padding-bottom:10px;
	  font-size:26px;
	   }
#stories .stories_1 h2 span{
    font-weight:900;
	color: #dc131b;
	   }
#stories .stories_1 h2:after{
   display: inline-block;
    width: 86%;
    height: 5px;
    background-color: #dc131b;
    vertical-align: middle;
    margin-left:1.5%;
	content:"";
}
  .stories_inner_main_1{
position:relative;
   }
.stories_inner_main_1:hover .stories_2_inner_1{
display:block;
   }

.stories_2_inner_1{
width:100%;
opacity:0.6;
height:100%;
background:#ffd80042;
position:absolute;
top:0;
display:none;
   }
   
   
.stories_2_middle h3{
padding:0;
margin:0;
padding-top:15px;
font-size:20px;
   }
.stories_2_middle h3 a{
color:#333333;
   }
.stories_2_middle h3 a:hover{
color:#dc131b;
   }
.stories_2_middle p{
padding:0;
margin:0;
color:#333333;
font-size:15px;
padding-top:10px;
   }
.stories_2_middle p i{
  margin-right:5px;
  }
.stories_2_middle p a{
color:#999;
   }
.stories_2_middle p a:hover{
color:#FF9900;
  }
  
/********************* stories_end****************/
/********************* product****************/
#product .col-sm-3{
 padding-left:0;
 padding-left:3px; 
  }
.product_main{
margin-bottom:20px;
  }
/********************* product_end****************/
/********************* trending****************/
#trending{
    padding-top:30px;
	}
#trending .trend_left{
    padding-left:0;
	}
#trending .trend_left_1{
    padding-right:0;
	}
.trending_top h2{
      padding:0;
	  margin:0; 
	  padding-bottom:20px;
	  font-size:26px;
	   }
.trending_top h2 span{
    font-weight:900;
	color:#dc131b;
	   }
.trending_top h2:after{
   display: inline-block;
    width: 80%;
    height: 5px;
    background-color: #dc131b;
    vertical-align: middle;
    margin-left:1.5%;
	content:"";
}
.trending_1{
   position:relative;
   }
.trending_1_text {
 top:0;
 height:100%;
 width:100%;
  position:absolute;
  padding-top:200px;
  padding-left:30px;
  background:#0000009e;
   }
 .trending_1_text:hover {
 background:#ffd80042;
   }
.trending_1_text p{
   padding:0;
   margin:0;
  background:  #dc131b;
  display:inline-block;
  padding:8px 20px 8px 20px;
  color:#ffffff;
   }
.trending_1_text h3{
   padding:0;
   margin:0;
   padding-top:10px;
   color:#ffffff;
   font-size:26px;
   }
.trending_1_text h5{
   padding:0;
   margin:0;
   color:#ffffff;
   font-size:16px;
   padding-top:10px;
   }
.trending_1_text h5 i{
  margin-right:5px;
  }
.trending_1_text h5 a{
   color:#ffffff;
   font-weight:bold;
   }
.trending_1_text h5 a:hover{
color:#ffd800;
   }
.trending_1_inner {
 padding-top:20px; 
  }
.trending_2_inner h6{
  padding:0;
  margin:0;
  font-weight:bold;
  color:#000000;
  padding-top:3px;
  text-align:center;
  }
.trending_2_inner h6 span{
  font-size:100px;
  color: #dc131b;
  }
.trending_2_inner_1 h4{
  padding:0;
  margin:0;
  font-size:24px;
  }
.trending_2_inner_1 h4 a{
color:#333333;
  }
.trending_2_inner_1 h4 a:hover{
color:#dc131b;
   }
.trending_2_inner_1 p{
  padding:0;
  margin:0;
  font-size:17px;
  padding-top:5px;
  }
.trending_2_inner_1 p a{
color:#999999;
  }
.trending_2_inner_2 img:hover{
opacity:0.8;
  }
.trending_2{
 padding-bottom:15px;
 margin-bottom:15px;  
   }
.just{
  background:#dc131b!important;
  }
/********************* trending_end****************/
/********************* contribute****************/
#contribute{
	padding-top:35px;
	padding-bottom:30px;
	background:#f4f4f4;
	margin-top:20px;
   }
#gallery{
	padding-top:35px;
	padding-bottom:30px;
	background:#f4f4f4;
	margin-top:20px;
   }
.contribute_1 h2{
      padding:0;
	  margin:0; 
	  padding-bottom:10px;
	  font-size:26px;
	  color:#ffffff;
	   }
.contribute_1 h2 span{
    font-weight:900;
	color:#ffffff;
	   }
 .contribute_1 h2:after{
   display: inline-block;
    width: 80%;
    height: 5px;
    background-color: #cccccc;
    vertical-align: middle;
    margin-left:1.5%;
	content:"";
}
.contribute_2{
padding-top:80px;
	   }

.contribute_2_inner a{
 color:#ffffff;
	   }

.contribute_2_inner a i{
  font-size:180px;
  color:#ffffff;
	   }
.contribute_2_inner_1 h1{
padding:0;
margin:0; 
 }
 .contribute_2_inner_1 h1 a{
padding:0;
margin:0;
color:#ffffff;
font-size:55px; 
 }

.contribute_3_inner img:hover{
opacity:0.8;
 }
.contribute_3_inner p{
padding:0;
margin:0;
padding-top:10px;
 }
.contribute_3_inner p a{
font-size:17px;
color:#333;
 }
.contribute_3_inner p a:hover{
color:#dc131b;
 }
 .contribute_3_inner_main{
position:relative;
  }
 .contribute_3_inner_text{
position:absolute;
top:0;
  }

.contribute_3_inner_text h5{
  padding:0;
  margin:0;
  background:#dc131b;
  display:inline-block;
  padding:8px 15px 8px 15px;
  color:#ffffff;
  font-size:12px;
  }
/********************* contribute_end****************/

/*********************production****************/
#production .stories_1 h2{
      padding:0;
	  margin:0; 
	  padding-bottom:10px;
	  font-size:26px;
	   }
#production .stories_1 h2 span{
    font-weight:900;
	color:#dc131b;
	   }
#production .stories_1 h2:after{
   display: inline-block;
    width: 80%;
    height: 5px;
    background-color: #dc131b;
    vertical-align: middle;
    margin-left:1.5%;
	content:"";
}
#production{
  padding-top:40px; 
  padding-bottom:20px;
   }
.production_main{
position:relative;
   }
.production_text{
position:absolute;
width:100%;
height:100%;
top:0;
text-align:right;
padding-right:10px;
background:#00000061;
   }
.production_text p{
padding:0;
margin:0;
font-size:18px;
font-weight:bold;
   }
.production_text p a{
color:#ffffff;
   }
.production_1 h3{
  padding:0;
  margin:0;
  padding-top:15px;
  font-size:22px; 
   }
.production_1 h3 a{
color:#000000;
   }
.production_1 h3 a:hover{
color:#dc131b;
   }
.production_1 p{
  padding:0;
  margin:0;
  padding-top:10px;
  font-size:18px; 
   }
.production_1 p a{
color:#666;
   }
   
 .production_main_1{
position:relative;
   }
.production_text_1{
position:absolute;
width:100%;
height:100%;
top:0;
background:#00000061;
   }
.production_text_1 p{
padding:0;
margin:0;
font-size:18px;
font-weight:bold;
   }
.production_text_1 p a{
color:#ffffff;
   }
.production_text_1 h6{
padding:0;
margin:0;
   }
.production_text_1 h6 a{
color:#ffffff;
background:#dc131b;
padding:8px 15px 8px 15px;
display:inline-block;
font-size:12px;
   }
.production_text_1_inner{
 padding:0;
  }
.production_text_1_inner_1{
 padding:0;
 padding-right:10px;
  }
.box_main{
  padding-bottom:25px;
  }

/* footer social icons */
ul.social-network {
	list-style: none;
	display: inline;
	margin-left:0 !important;
	padding: 0;
}
ul.social-network li {
	display: inline-block;
}


/* footer social icons */
.social-network a.icoRss:hover {
	background-color: #F56505;
}
.social-network a.icoFacebook:hover {
	background-color:#3B5998;
}
.social-network a.icoTwitter:hover {
	background-color:#33ccff;
}
.social-network a.icoGoogle:hover {
	background-color:#BD3518;
}
.social-network a.icoVimeo:hover {
	background-color:#0590B8;
}
.social-network a.icoLinkedin:hover {
	background-color:#007bb7;
}
.social-network a.icoRss:hover i, .social-network a.icoFacebook:hover i, .social-network a.icoTwitter:hover i,
.social-network a.icoGoogle:hover i, .social-network a.icoVimeo:hover i, .social-network a.icoLinkedin:hover i {
	color:#fff;
}
a.socialIcon:hover, .socialHoverClass {
	color:#44BCDD;
}

.social-circle li a {
	display:inline-block;
	position:relative;
	margin:0 auto 0 auto;
	-moz-border-radius:50%;
	-webkit-border-radius:50%;
	border-radius:50%;
	text-align:center;
	width: 40px;
	height: 40px;
	font-size:16px;
}
.social-circle li i {
	margin:0;
	line-height:40px;
	text-align: center;
}

.social-circle li a:hover i, .triggeredHover {
	-moz-transform: rotate(360deg);
	-webkit-transform: rotate(360deg);
	-ms--transform: rotate(360deg);
	transform: rotate(360deg);
	-webkit-transition: all 0.2s;
	-moz-transition: all 0.2s;
	-o-transition: all 0.2s;
	-ms-transition: all 0.2s;
	transition: all 0.2s;
}
.social-circle i {
	color: #fff;
	-webkit-transition: all 0.8s;
	-moz-transition: all 0.8s;
	-o-transition: all 0.8s;
	-ms-transition: all 0.8s;
	transition: all 0.8s;
}

.production_1_inner ul li a {
 background-color: #999;   
}
.production_1_inner ul  {
display:block;
margin-top:15px;  
}
/********************* production_end****************/
/********************* more****************/
#more{
	padding-top:10px;
	}
.more_1 h5{
   padding:0;
   margin:0;
   font-size:16px;
   color:#000000;
   }
.more_1 h5 span{
  font-weight:bold;
   }
 .more_2 {
padding-left:0;
   }
.more_2 a{
  font-size:17px;
  color:#767676;
   }
.more_2 a:hover{
color:#FF6600;
   }
   
.stories_bottom{
padding:0;
  }
.stories_bottom .col-sm-6{
padding:0;  
  }
.text_1{
 padding-top:0;
 padding-top:260px;  
   }
.just_1{
background:#dc131b!important;  
  }
/********************* more_end****************/

/*********************magazine****************/
.clear_1{
padding-bottom:0!important;  
  }
#magazine{
	   background-repeat:no-repeat;
	   padding-top:30px;
	   background: #f4f4f4;
   }
.magzine_1 h1{
padding:0;
margin:0;
color:#333;
font-size:55px;  
  }
.magzine_1 p{
padding:0;
margin:0;
color:#000000;
font-size:21px;
padding-top:15px; 
  }
.magzine_1 h4{
padding:0;
margin:0;
padding-top:30px;
  }
.magzine_1 h4 a{
color:#fff;
font-size:20px;
font-weight:900;
padding:10px 20px 13px 20px;
border-radius:3px;
display:inline-block;
background:#dc131b;
border:2px solid #dc131b;
  }
.magzine_1 h4 a:hover{
  background:none;
  color:#dc131b;
  }
.magzine_2{
 padding-top:15px;
 padding-bottom:15px;
 background:#fff;
 margin-top:10px;
  }
.magzine_2_inner p{
  padding:0;
  margin:0;
  color:#333;
  font-size:18px;
  }
.magzine_2_inner p a{
  color:#dc131b;
  font-size:20px;
  font-weight:bold;
  }
.magzine_3{
 padding-top:5px; 
   }
 .magzine_3 a{
color:#666;
font-size:17px;
   }
 .magzine_3 a:hover{
  color:#FF9900;
   }
 .magzine_3 span{
color:#cccccc;
padding-left:4px;
padding-right:4px;
   }
.mag_n h3{
  padding:0;
  margin:0;
  margin-bottom:15px;
  }
.mag_n{
 margin-bottom:20px; 
  }
.mag_n ul li{
  font-size:16px;
  margin-bottom:5px;
  }
.mag_n ul li a{
 color:#666;
  }
.mag_n ul li a:hover{
 color:#dc131b;
  }
/********************* magazine_end****************/

/*********************center_blog****************/
#center_blog{
    background: url(../img/71.jpg);
	background-repeat:no-repeat;
   }
.center_blog_m{
  background:#0009;
  padding-top:30px;
  padding-bottom:40px;
  }
.center_blog h1{
   padding:0;
   margin:0;
   color:#ffffff;
   font-size:60px;
   }
.center_blog h3{
   padding:0;
   margin:0;
   color:#eee;
   padding-top:5px;
   }
.center_blog p{
   padding:0;
   margin:0;
   color:#e1e1e1;
   padding-top:10px;
   font-size:16px;
   }
.center_blog p a{
 color:#ffffff;
 font-weight:bold;
   }
#carousel-example-generic{
  width:70%;
  margin:auto;
  margin-top:15px;
  }
#center_blog .img-responsive,
#center_blog .thumbnail > img,
#center_blog .thumbnail a > img,
#center_blog .carousel-inner > .item > img,
#center_blog .carousel-inner > .item > a > img {
  display: block;
  width: 100%;
  height: auto;
}

/* ------------------- Carousel Styling ------------------- */

#center_blog .carousel-inner {
  border-radius:5px;
}

#center_blog .carousel-caption {
  background-color: rgba(0,0,0,.5);
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  padding: 0 0 10px 25px;
  color: #fff;
  text-align: left;
}

#center_blog .carousel-indicators {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  width: 100%;
  z-index: 15;
  margin: 0;
  padding: 0 25px 25px 0;
  text-align: right;
}

#center_blog .carousel-control.left,
#center_blog .carousel-control.right {
  background-image: none;
}


/* ------------------- Section Styling - Not needed for carousel styling ------------------- */

#center_blog .section-white {
   padding: 10px 0;
}

#center_blog .section-white {
  background-color: #fff;
  color: #555;
}

@media screen and (min-width: 768px) {

 #center_blog  .section-white {
     padding: 1.5em 0;
  }

}

/*********************center_blog_end****************/

/*********************world****************/
#world{
  padding-top:30px;
  }
.world_left h4{
   padding:0;
   margin:0;
   font-size:20px;
   font-weight:bold;
   padding-bottom:20px;
   margin-bottom:15px;
   }
.world_left h4 span{
border-bottom:5px solid #dc131b;
padding-bottom:10px;
   }
.world_left_inner{
position:relative; 
margin-bottom:20px; 
  }
.world_left_inner_text{
  position:absolute; 
  top:0;
  padding-top:100px;
  padding-left:10px;
  width:100%;
  height:100%;
  background:#0000006b;
  }
.world_left_inner_text a{
color:#ffffff;
font-size:15px;
  }
.world_left_inner_text:hover{
background:#000000;
opacity:0.6;
  }
.world_left h5{
   padding:0;
   margin:0;
   font-size:16px;
   font-weight:900;
   }
 .world_left h5 a{
color:#000000;
   }  
   
.world_left h6{
   padding:0;
   margin:0;
   font-size:17px;
   margin-top:15px;
   color:#767676;
   }
 .world_left h6 a{
   color:#767676;
   }
 .world_left h6 a:hover{
   color:#FF6600;
   }
.world_left h6 span{
border-bottom:2px solid #dc131b;
padding-bottom:10px;
   }
.social a{
padding:10px 0px 10px 0px;
color:#ffffff;
border-radius:3px;
font-size:25px;
margin-right:5px;
display:block;
width:45%;
margin-bottom:10px;
}
.social {
padding-top:30px;
position:inherit;
}
.social .tag_1{
background:#333333;
}
.social .tag_1:hover{
background:#000000;
}
.social .tag_2{
background:#bd081c;
}
.social .tag_2:hover{
background:#8a0817;
}
.social .tag_3{
background:#385a98;
}
.social .tag_3:hover{
background:#304e84;
}
.social .tag_4{
background:#55acee;
}
.social .tag_4:hover{
background:#498fc3;
}
.social .tag_5{
background:#0976b4;
}
.social .tag_5:hover{
background:#3b75a0;
}
.world_middle_inner p{
  padding:0;
  margin:0;
  font-size:18px;
  padding-bottom:15px; 
   }
.world_middle_inner p span{
color:#000000;
font-weight:900;
   }
.world_middle_inner p .span_1{
color:#cccccc!important;
margin-left:5px;
margin-right:5px;
   }
.world_middle_inner p a{
color:#990000; 
   }
.world_middle_inner p a:hover{
color:#FF6600;
   }
.world_middle_inner{
border-bottom:1px solid #e1e1e1;
padding-bottom:20px;
   }
.world_middle_inner_1{
padding-top:20px;
border-bottom:1px solid #e1e1e1;
padding-bottom:20px;
   }
.world_middle_inner_1 h6{
padding:0;
margin:0;
   }
.world_middle_inner_1 h6 a{
color:#767676;
font-size:14px;
padding-right:5px;
   }
.world_middle_inner_1 h6 a:hover{
color:#FF6600;
   }
.world_middle_inner_1 p{
padding:0;
margin:0;
margin-top:30px;
font-size:18px;
   }
.world_middle_inner_1 .p1{
padding:0;
margin:0;
margin-top:30px;
font-size:18px;
border-left:12px solid #dc131b;
padding-left:12px;
   }
.world_middle_inner_1 p a{
color:#990000;
font-weight:bold;
   }
.world_middle_inner_1 p a:hover{
color:#FF9900;
   }
.world_middle_inner_1 img{
margin-top:20px;
   }
.world_middle_inner_1 img:hover{
opacity:0.8;
   }
.world_middle_inner_1 h4{
padding:0;
margin:0;
padding-top:20px;
font-size:22px;
   }
.world_middle_inner_1 h5{
padding:0;
margin:0;
padding-top:10px;
color:#767676;
   }
.world_middle_inner_2 p{
   padding:0;
   margin:0;
   padding-top:20px;
   font-size:18px;
   }
 .world_middle_inner_2 h4{
   padding:0;
   margin:0;
   padding-top:40px;
   font-size:21px;
   font-weight:bold;
   padding-bottom:10px;
   }
.table_1 {
border-bottom:1px solid #e1e1e1;
  }
.table_1 {
border-bottom:1px solid #e1e1e1;
  }
.table_1 p{
  padding:0;
  margin:0;
  font-size:15px;
  padding-bottom:5px;
  padding-top:5px;
  }
.world_middle_inner_2 h2{
   padding:0;
   margin:0;
   padding-top:30px;
   font-weight:bold;
   }
.world_middle_inner_2 p a{
color:#990000;
font-weight:bold;
   }
.world_middle_inner_2 p a:hover{
color:#FF9900;
   }
.world_middle_inner_2 img{
margin-top:20px;
   }

.world_middle_inner_2 img:hover{
opacity:0.8;
   }
.world_middle_inner_2 .heading_main{
padding:0;
margin:0;
padding-top:20px;
font-size:22px;
font-weight:100;
   }
.world_middle_inner_2 h5{
padding:0;
margin:0;
padding-top:10px;
color:#767676;
border-bottom:1px solid #e1e1e1;
padding-bottom:20px;
   }
.continue{
   padding-top:25px;
   }
.continue_1{
   padding-left:0;
   }
.continue_1 a{
  background:#dc131b;
  padding:10px 20px 12px 20px;
  font-size:18px;
  color:#ffffff;
  border-radius:3px;
   }
 .continue_1 a:hover{
 background:#6d0707
   }
.continue_2 span{
font-size:18px;
font-weight:bold;
   }
.continue_2 span a{
color:#990000;
   }
.continue_2 span a:hover{
color:#FF9900;
   }

 .detail_page_2 .btn{
        background:#dc131b;
		border-color:#dc131b;
			 }
.detail_2_inner{
 padding-top:30px; 
  }
 .detail_2_inner h6{
padding:0;
margin:0;
font-weight:900; 
font-size:18px;
margin-bottom:10px;
  }
.detail_2_inner h6 i{
font-size:20px;
color:#009900;
margin-right:13px;
  }
 .detail_2_inner h5 {
padding:0;
margin:0;
padding-top:12px;
  }
 .detail_2_inner h5 a {
color:#333333;
font-size:16px;
font-weight:900;
  }
 .detail_2_inner h5 a:hover {
color:#dc131b;
  }
 .detail_2_inner p {
padding:0;
margin:0;
padding-top:7px;
border-bottom:1px solid #e1e1e1;
padding-bottom:10px;
  }
.detail_2_inner p a{
    color: #767676;
	font-size:15px;
  }

.detail_2_inner_1{
 padding-top:30px; 
  }
 .detail_2_inner_1 h6{
padding:0;
margin:0;
font-weight:900; 
font-size:16px;
  }
.detail_2_inner_1 h6 i{
font-size:20px;
color:#009900;
margin-right:13px;
  }
 .detail_2_inner_1 h5 {
padding:0;
margin:0;
padding-top:12px;
border-bottom:1px solid #e1e1e1;
padding-bottom:10px;
line-height:1.4em;
  }
 .detail_2_inner_1 h5 a {
color:#333333;
font-size:14px;
  }
 .detail_2_inner_1 h5 a:hover {
color:#dc131b;
  }
  .detsil_2_text{
   margin-top:30px;
   background-color: #47a5c9;
   padding:20px 20px 60px 20px;
   }
 .detsil_2_text p{
 padding:0;
 margin:0;
 font-size:18px;
 color:#ffffff;
   }
 .detsil_2_text img{
margin-bottom:5px;
   }
  .detsil_2_text h1{
 padding:0;
 margin:0;
padding-top:10px;
color:#ffffff;
font-weight:bold;
font-size:37px;
margin-bottom:10px;
   }
.detsil_2_text h3{
padding:0;
margin:0;
padding-top:10px;
color:#ffffff;
font-weight:bold;
}
.detsil_2_text h5{
padding:0;
margin:0;
padding-top:30px;
}
.detsil_2_text h5 a{
color:#ffffff;
background:#333333;
padding:13px 30px 13px 30px;
font-size:16px;
border-radius:3px;
}
.detsil_2_text h5 a:hover{
background:#000000;
}
.world_bottom{
     padding-top:30px;
	 }
.world_bottom_1 h2{
      padding:0;
	  margin:0; 
	  padding-bottom:10px;
	  font-size:26px;
	   }
.world_bottom_1 h2 span{
    font-weight:900;
	color:#dc131b;
	   }
.world_bottom_1 h2:after{
   display: inline-block;
    width: 75%;
    height: 5px;
    background-color: #dc131b;
    vertical-align: middle;
    margin-left:1.5%;
	content:"";
}
.world_bottom_2 .col-sm-3{
  padding-left:0;
  padding-left:5px;
  }
.world_bottom_2_inner{
  position:relative;
  }
.world_bottom_2_inner:hover .world_bottom_2_inner_text {
background:#000000;
opacity:0.6;
  }
.world_bottom_2_inner_text{
 position:absolute;
 top:0;
 padding-top:113px;
padding-left:10px;
width:100%;
height:100%;
 }
.world_bottom_2_inner_text a{
color:#ffffff;
 }
.world_bottom_2_inner_1 h4{
  padding:0;
  margin:0;
  font-size:21px;
  padding-top:8px;
  }
.world_bottom_2_inner_1 h4 a{
color:#333333;
  }
.world_bottom_2_inner_1 h4 a:hover{
color:#dc131b;
  }
.world_bottom_2_inner_1 h5 {
padding:0;
margin:0;
padding-top:8px;
color:#999999;
font-size:16px;
  }
.world_bottom_2_inner_1 h5 a {
color:#990000;
font-weight:bold;
  }
.world_bottom_2_inner_1 h5 a:hover {
color:#FF6600;
  }
.world_bottom_2_inner_1 p {
padding:0;
margin:0;
padding-top:8px;
font-size:17px;
  }
.world_bottom_3{
  padding-top:40px;
  }
.world_bottom_3_inner{
padding-left:0;  
  }
.world_bottom_3_inner .col-sm-2{
padding-left:0;  
  }
.world_bottom_3_inner_2 h4{
  padding:0;
  margin:0;
  display:inline;
  padding-right:5px;
  }
.world_bottom_3_inner_2 h4 a{
color:#000000;
font-weight:bold;
font-size:21px;
  }
.world_bottom_3_inner_2 h4 a span{
border-top:2px solid #dc131b;
  }
.world_bottom_3_inner_2 .p2{
  padding:0;
  margin:0;
  display:inline;
  padding-left:5px;
  }
.world_bottom_3_inner_2 .p2 a{
margin-right:5px;
font-size:18px;
  }
.world_bottom_3_inner_2 .p2 .a1{
color:#0099FF;
  }
.world_bottom_3_inner_2 .p2 .a2{
color:#0066FF;
  }
.world_bottom_3_inner_2 .p2 .a3{
color:#CC3300;
  }
.world_bottom_3_inner_2 .p3{
padding:0;
margin:0;
font-size:18px;
padding-top:10px;
  }
.world_bottom_3_inner_2 .p3 a{
color:#990000;
font-weight:bold;
  }
.world_bottom_3_inner_2 .p3 a:hover{
color:#FF6600;
  }
.world_bottom_inner_3 {
   margin-top:30px;
	   }
.world_bottom_inner_3 h2{
      padding:0;
	  margin:0; 
	  padding-bottom:10px;
	  font-size:26px;
	   }
.world_bottom_inner_3 span{
    font-weight:900;
	color:#dc131b;
	   }
.world_bottom_inner_3 h2:after{
   display: inline-block;
    width: 51%;
    height: 5px;
    background-color: #dc131b;
    vertical-align: middle;
    margin-left:1.5%;
	content:"";
}
.world_bottom_inner_4_inner p{
  padding:0;
  margin:0;
  padding-top:15px;
  font-size:17px;
  }
.world_bottom_inner_4_inner p a{
  color:#333333;
  }
.world_bottom_inner_4_inner p a:hover{
  color:#FF6600;
  }

/*********************world_end****************/
/*********************detail_news****************/
#detail_news{
  margin-top:30px; 
   }
.detail_news h5{
  padding:0;
  margin:0;
  color:#333;
   border-bottom:5px solid #dc131b;
   padding-bottom:10px;
   font-size:20px;
  }
.detail_news_inner_text p{
  padding:0;
  margin:0;
  padding-top:15px;
  }
.detail_news_inner_text p a{
color:#333333;
font-size:16px;
  }
.detail_news_inner_text p a:hover{
color:#dc131b;
  }
.detail_news_inner_text h6{
  padding:0;
  margin:0;
  padding-top:5px;
  color:#999999;
  font-size:14px;
  border-bottom:1px solid #eee;
  padding-bottom:15px;
  }
.detail_news_inner_text h6 i{
 margin-right:5px;
 font-size:18px;
 vertical-align:middle;
  }
.detail_news_inner_1 h2{
 padding:0;
 margin:0;
 padding-top:10px;
font-size:35px; 
  }
.detail_news_inner_1 h2 a{
color:#000000;
  }
.detail_news_inner_1 h2 a:hover{
 color:#dc131b;
  }
.detail_news_inner_1 img{
 padding-top:15px; 
  }
.detail_news_inner_1 p{
 paddding:0;
 margin:0;
 font-size:16px;
 padding-top:5px;
  }
.detail_news_inner_1 .p4{
 padding:0;
 margin:0;
 font-size:16px;
 padding-top:5px;
 color:#767676;
  }
.detail_news_inner_1 .p4 a{
color:#990000;
font-weight:bold;
  }
.detail_news_inner_1 .p4 a:hover{
color:#FF6600;
  }
.detail_news_inner_1_middle{
 padding-top:10px; 
  }
.detail_news_inner_1_middle .col-sm-6{
 padding-left:0; 
 padding-left:5px;
  }

.detail_news_inner_1_middle h2{
font-size:27px;
font-weight:bold; 
  }
.detail_news_inner_right_1_inner{
 padding-top:15px;
  }
.detail_news_inner_right_1_inner p{
  padding:0;
  margin:0;
  font-size:17px;
  font-weight:bold;
  padding-bottom:10px;
  }
.detail_news_inner_right_1_inner {
padding-left:0;
  }
.detail_news_inner_right_1_inner p a{
 color:#000000;
  }
.detail_news_inner_right_1_inner p a:hover{
 color:#FF6600;
  }
.forums{
 padding-top:20px; 
  }
.forums p{
padding:0;
margin:0;
padding-top:10px;
  }
.forums p a{
font-weight:bold;
font-size:17px;
padding-left:5px;
padding-right:5px;
color:#000000;
  }
.forums p i{
color:#767676;
padding-right:3px;
  }
.forums p .span_2{
color:#990000;
font-weight:900;
font-size:16px;
padding-left:3px;
padding-right:3px;
  }
.forums p .span_3{
color:#767676;
font-size:16px;
  }
.product{
  margin-top:30px;
  }
.product_inner{
  padding-top:20px;
  }
.product_inner_1{
  padding-left:0;
  }
.product_inner_1 img:hover{
  border:2px solid #FF6600;
  }
.product_inner_2 {
padding-left:0;
  }
.product_inner_2 h4{
  padding:0;
  margin:0;
  }
.product_inner_2 h4 a{
 color:#000000;
 font-weight:bold;
 font-size:18px;
  }
.product_inner_2 h4 a:hover{
color:#FF9900;
  }
.product_inner_2 p{
padding:0;
margin:0;
font-size:16px;
  }
.product_inner_2 p a{
color:#990000;
font-weight:bold;
  }
.product_inner_2 p a:hover{
color:#FF6600;
  }
.product_inner_2 h6{
padding:0;
margin:0;
padding-top:3px;
  }
.product_inner_2 h6 a{
color:#990000;
font-size:14px;
  }
.detail_last{
  margin-top:30px;
  border-top:1px solid #e1e1e1;
  padding-top:30px;
  }  

/*********************detail_news_end****************/
/********************* contact ***************/
#contact{
  padding-top:15px;
  }
.contact h2{
font-size:30px;
font-weight:700;
color: #0e2d45;
   }
.contact .boat_1{
border-bottom:2px solid #dc131b;
padding-bottom:10px;
   }
.contact_1{
padding:0;
margin:0;
padding-top:30px;
padding-bottom:10px;
  }
.contact_1_left_top{
padding:0;
margin:0;
padding-top:15px;
padding-bottom:10px;
background:#f6f6f6;
min-height:160px;
   }
.contact_1_left_top_1{
padding:0;
margin:0;
   }
.contact_1_left_top_1 p i{
font-size:36px;
border-radius:50%;
background:#dc131b;
color:#FFFFFF;
margin-top:10px;
width:90px;
height:90px;
text-align:center;
line-height:90px;
margin-left:20px;
   }
.contact_1_left_top_2 h3{
font-size:18px;
font-weight:700;
   }
.contact_1_left_top_2 p{
font-size:16px;
color:#60707d;
line-height:25px;
   }
.contact_1_left_top_1 .mobile i{
width:90px;
height:90px;
font-size:55px;
text-align:center;
line-height:90px;
  }
.contact_1_left_top_2 p a{
font-size:16px;
text-decoration:none;
color:#dc131b;
   }
.contact_1_left_top_2 p a:hover{
color:#FF6600;
text-decoration:none;
   }
.contact_2{
padding-bottom:20px;
  }
.contact_2_left h2{
font-size:22px;
font-weight:700;
padding-bottom:10px;
}
.contact_2_left .form-control {
    display: block;
    width: 100%;
    height: 50px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    -webkit-transition: border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
}
.contact_2_left .form-control_3{
width:100%;
height:170px;
margin-bottom:20px;
padding-left:10px;
border: 1px solid #ccc;
  }
#contact .btn{
  background:#dc131b;
  border:none;
  padding:12px 25px;
  font-size:16px;
  border-radius:0;
  display:inline-block;
  }
#contact .btn:hover{
  background:#FF6600;
  }
  
.about_1_right_center{
padding:0;
margin:0;
  }
.about_1_right_center_1{
padding:0;
margin:0;
margin-top:30px;
  }
.about_1_right_center_1 h2{
padding:0;
margin:0;
font-size:18px;
font-weight:600;
color:#FFFFFF;
background:#dc131b;
padding-top:10px;
padding-bottom:10px;
padding-left:10px;
  }
.about_1_right_center_2{
padding:0;
margin:0;
background:#fafafa;
padding-bottom:10px;
  }
.about_1_right_center_2 p{
font-size:16px;
padding-top:20px;
padding-left:10px;
padding-right:10px;
color:#666666;
line-height:25px;
   }
.about_1_right_center_2 h3{
padding-left:10px;
   }
.about_1_right_center_2 h3 a{
font-size:18px;
font-weight:600;
color:#333;
text-decoration:none;
   }
.about_1_right_center_2 h5{
font-size:14px;
padding-left:10px;
color:#999;
   }
/********************* contact_end ***************/

/*********************pages****************/
#pages{
 padding-top:10px;
 padding-bottom:10px;
 }
 #pages .typo{
font-size:45px;
font-weight:bold;
 }
/*********************pages_end****************/

@media screen and (max-width : 767px){
br{
 display:none; 
  }
 .container-fluid{
   padding-left:20px;
   padding-right:20px;
	}
#header{
   position:static;
   }
.navbar-toggle{
 background:#dc131b;
 margin-top:22px;  
  }
.navbar-toggle .icon-bar{
 background:#fff;  
  }
.navbar-collapse {
box-shadow:none;
border:none!important;  
  }
.drop_1{
 background:#fff!important; 
 margin-top:0!important;
 min-width:auto!important;
  }
.drop_2{
 background:#fff!important; 
  }
#header ul li a{
 margin-left:0;
 margin-right:0;
 font-size:28px;
 padding-top:20px;
 padding-bottom:20px;
 border-bottom:1px solid #ddd; 
  }
#header ul li ul li a{
 border:none; 
  }
.navbar-nav{
text-align:center;
  }
#center{
text-align:center;
   }
.center_home_1i h1{
  line-height:1em;
  }
.center_1{
  margin-bottom:10px; 
   }
#stories{
 text-align:center; 
  }
#product{
 text-align:center; 
  }
#product .col-sm-3{
  padding-left:15px;
  }
#trending .trend_left{
  padding-left:15px;
  }
#trending .trend_left_1{
  padding-right:15px;
  }
#trending{
 text-align:center; 
  }
.trending_1{
margin-bottom:20px; 
}
.trending_1_text {
top:0;
height:100%;
width:100%;
position:static;
padding:0;
opacity:0.8;
background:#333;
padding:20px;
   }
.trending_1_text:hover{ 
background:#333;  
  }
.stories_2_middle{
 margin-bottom:20px; 
  }
.trending_2_inner_1{
  margin-top:10px;
  }
.trending_2_inner_2 img{
  margin-top:10px;
  }
#contribute{
text-align:center; 
  }
.contribute_2{
 padding-top:0;
 padding-top:20px;
  }
.contribute_3_inner{
margin-bottom:20px;
  }
.production_1{
margin-bottom:10px;  
  }
#gallery{
text-align:center; 
	}
.production_1_inner ul{
 margin-bottom:20px;
 }
#more{
  text-align:center; 
  }
.more_1{
padding-left:15px!important;  
  }
.more_2{
padding-left:15px;  
  }
#center_blog{
text-align:center;  
  }
#carousel-example-generic{
  width:100%;
  }
.carousel-caption h2{
 font-size:24px;
 }
#world{
  text-align:center;
  }
.world_left_inner_text{
  padding-top:78%;
  }
.social a{
 display:inline-block;
 margin-right:0;  
	}
.continue_1 a {
   padding: 10px 15px 12px 15px;
   }
.continue_2 {
 text-align:center;
 padding-top:10px;
 padding-bottom:10px; 
  }
.detsil_2_text h5 a{
 padding:13px 10px; 
 font-size:14px;
  }
.world_bottom_2 .col-sm-3{
 padding-left:15px; 
  }
.world_bottom_3_inner_1{
margin-bottom:20px; 
 }
.world_bottom_inner_4_inner img{
margin-top:10px;  
  }
.world_bottom_2_inner{
margin-top:10px;
  }
.world_bottom_2_inner_text{
height:100%;
display:none;
  }
.world_bottom_3_inner .col-sm-2{
 padding-left:15px; 
 }
.world_bottom_3_inner{
   padding-left:15px; 
  }

.detail_news_inner{
 margin-bottom:20px; 
  }
.detail_news_inner_right{
 margin-top:20px;  
   }
 .forums{
 padding-top:0; 
 padding-top:20px; 
   }
.product_inner_1 img{
margin-bottom:15px; 
 }
.more_1{
 padding-left:0; 
padding-bottom:10px;
  }
.contact_3 i {
   
    padding-right:0;
	 padding-right:10px;
}	
.contact_3  {
   
    margin-bottom:20px;
}
#pages p .btn{
      margin-bottom:10px;
	  }
 #pages  .label {
 
    font-size: 50%;
 
}  
#pages img{
      width:100%;
	  margin-top:10px;
	  }		
#pages .typo{
     font-size:35px;
	  }
#contact{
 margin:0; 
  }
#pages{
 margin:0; 
  }
#detail_news{
text-align:center;
  }
#production{
 text-align:center; 
  }
.production_text{
padding:0;
padding:5px;
   }
.production_text_1_inner_1{
 display:none; 
  }
#magazine{
  text-align:center;
  }
.magzine_1 h4{
 margin-bottom:15px; 
  }
.mag_n ul li {
display:inline-block; 
 }
.detail_news_inner_1_middle .col-sm-6{
padding-left:15px;  
  }
.detail_news_inner_1 h2{
font-size:26px; 
 }
.product_inner_1{
padding-left:15px;  
  }
.product_inner_2{
padding-left:15px;  
  }
.detail_news_inner_right_1_inner{
 padding-left:15px; 
 }
#contact{
  text-align:center;
  }
.contact_1_left_top_1 p i{
margin-left:0;  
  }
.contact_1_left_top{
 margin-top:10px; 
  }
#contact .btn{
 float:none!important; 
  }
}
@media (min-width:768px) and (max-width:991px)  {
.container-fluid{
 padding-left:15px;
 padding-right:15px;
 }
#header ul li a{
padding-left:8px;
padding-right:8px;
margin-left:2px;
margin-right:2px;
font-size:15px;  
  }
#header .navbar-brand{
 margin-right:10px; 
  }
.center_home_1i h1{
line-height:1em; 
 }
#stories .stories_1 h2:after{
width:76%;  
  }
.trending_1_text{
padding-top:0; 
padding-top:5px;
 }
.trending_top h2:after{
  width:72%; 
  }
#production .stories_1 h2:after{
  width:67%;
  }
.production_text_1 p{
 font-size:12px;  
   }
ul.social-network li{
 margin-top:5px; 
  }
.magzine_1 h4 a{
 margin-bottom:20px; 
  }
.detail_news_inner_1_middle h2{
 font-size:22px; 
  }
.world_left h4 span{
border:none; 
  }
.world_left h4 {
 border-bottom:5px solid #dc131b;
 padding-bottom:5px;
  }
.world_left_inner_text{
 display:none; 
  }
.world_left h6 span{
 border:none; 
  }
.continue_1 a {
    background: #dc131b;
    padding: 10px 12px 12px 12px;
	font-size:16px;
	  }
.world_bottom_1 h2:after{
 width:57%; 
  }
.world_bottom_inner_3 h2:after{
   width:10%;
  }
.world_bottom_2_inner_text{
 display:none; 
  }
.world_bottom_2_inner_1 h5{
 line-height:1.6em; 
  }
.detsil_2_text img{
 width:100%; 
  }
.detsil_2_text h5 a{
padding:8px;
font-size:12px;  
  }
.detsil_2_text{
 padding-left:10px;
 padding-right:10px; 
  }
.contact_1_left_top_1 p i{
width:50px;
height:50px;
line-height:50px;
font-size:18px;  
  }
.contact_1_left_top_1 .mobile i{
 width:50px;
height:50px;
line-height:50px;
font-size:28px;
  }
#pages{
 overflow:hidden; 
  }
}
@media (min-width:992px) and (max-width:1200px)  {
.container-fluid{
 padding-left:15px;
 padding-right:15px;
 }
.center_home_1i h1{
line-height:1em; 
 }
#stories .stories_1 h2:after{
width:81%;  
  }
.trending_1_text{
padding-top:0; 
padding-top:5px;
 }
.trending_top h2:after{
  width:74%; 
  }
#production .stories_1 h2:after{
  width:74%;
  }
.production_text_1 p{
 font-size:12px;  
   }
ul.social-network li{
 margin-top:5px; 
  }
.world_left h4 span{
border:none; 
  }
.world_left h4 {
 border-bottom:5px solid #dc131b;
 padding-bottom:5px;
  }
.world_left_inner_text{
 display:none; 
  }
.world_bottom_2_inner_text{
  display:none; 
  }
.world_bottom_1 h2:after{
 width:67%; 
  }
.world_bottom_inner_3 h2:after{
   width:33%;
  }
.detsil_2_text h5 a{
 padding:13px 15px;
 font-size:14px; 
  }
.contact_1_left_top_1 p i{
width:70px;
height:70px;
line-height:70px;
font-size:22px;  
  }
.contact_1_left_top_1 .mobile i{
 width:70px;
height:70px;
line-height:70px;
font-size:30px;
  }
#pages{
 overflow:hidden; 
  }
}
@media (min-width:1201px) and (max-width:1320px)  {
 #stories .stories_1 h2:after{
   width:84%; 
	}
.trending_1_text{
padding-top:170px;  
  }
#production .stories_1 h2:after{
  width:77%;
 }
.world_left_inner_text{
 padding-top:60px; 
  }
.world_bottom_1 h2:after{
 width:71%; 
  }
.world_bottom_inner_3 h2:after{
   width:43%;
  }
.world_bottom_2_inner_text{
 padding-top:90px; 
  }
.detsil_2_text h5 a{
 padding:13px 20px; 
  }
#pages{
 overflow:hidden; 
  }
  }

